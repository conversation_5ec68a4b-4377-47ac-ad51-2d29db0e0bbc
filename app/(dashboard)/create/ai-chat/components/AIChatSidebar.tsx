"use client";

import { useState, useEffect, useCallback } from "react";
import { useUser } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import { CreditCard, Sparkles } from "lucide-react";
import { toast } from "sonner";
import { useConversation } from "../hooks/useConversation";
import { useAIAgent } from "../hooks/useAIAgent";
import { useStreamingAgent } from "../hooks/useStreamingAgent";
import MessageBubble from "./MessageBubble";
import StreamingMessage from "./StreamingMessage";
import ChatInput from "./ChatInput";
import { UserContext, ContentItem } from "../types/conversation";

interface AIChatSidebarProps {
  onStructuredOutput?: (output: any) => void;
  isMobile?: boolean;
}

// Helper function to aggregate multiple tool results from parallel tool calls
function aggregateToolResults(toolResults: any[]): any {
  if (!toolResults || toolResults.length === 0) {
    return undefined;
  }

  // If single result, return as-is
  if (toolResults.length === 1) {
    return toolResults[0].data;
  }

  // Aggregate multiple results
  const aggregated: any = {
    success: toolResults.every(result => result.data?.success),
    images: [],
    creditsUsed: 0,
    creditsRemaining: undefined
  };

  // Collect all images and sum credits
  for (const result of toolResults) {
    if (result.data?.success) {
      // Handle both single image and images array
      if (result.data.image) {
        aggregated.images.push(result.data.image);
      }
      if (result.data.images) {
        aggregated.images.push(...result.data.images);
      }
      
      // Sum credits used
      if (result.data.creditsUsed) {
        aggregated.creditsUsed += result.data.creditsUsed;
      }
      
      // Keep latest credits remaining
      if (result.data.creditsRemaining !== undefined) {
        aggregated.creditsRemaining = result.data.creditsRemaining;
      }
    }
  }

  return aggregated;
}

export default function AIChatSidebar({ onStructuredOutput, isMobile = false }: AIChatSidebarProps) {
  const { isSignedIn, isLoaded } = useUser();
  const [userContext, setUserContext] = useState<UserContext | null>(null);
  const [isLoadingContext, setIsLoadingContext] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Conversation management
  const {
    messages,
    isTyping,
    addMessage,
    setTyping,
    setUserContext: setConversationUserContext,
    addUploadedFiles,
    setProductImage,
    getConversationHistory,
    messagesEndRef
  } = useConversation();

  // Streaming agent for modern UX
  const {
    streamingState,
    streamMessage,
    retryToolCall,
    resetStreamingState
  } = useStreamingAgent(userContext || undefined);

  // Legacy AI Agent integration (for fallback)
  const {
    isProcessing,
    sendWelcomeMessage,
    fetchUserContext
  } = useAIAgent({
    onMessage: addMessage,
    onTypingChange: setTyping,
    userContext: userContext || undefined,
    conversationHistory: getConversationHistory()
  });

  // Fetch user context and credits
  const loadUserContext = useCallback(async () => {
    try {
      setIsLoadingContext(true);
      
      // Ensure user document exists first
      await fetch("/api/users", { method: "POST" });
      
      // Fetch user context
      const context = await fetchUserContext();
      
      if (context) {
        setUserContext(context);
        setConversationUserContext(context);
      }
    } catch (error) {
      console.error('[AIChatSidebar] Error loading user context:', error);
      toast.error('Failed to load user information');
    } finally {
      setIsLoadingContext(false);
    }
  }, [fetchUserContext, setConversationUserContext]);

  // Initialize conversation when component mounts
  useEffect(() => {
    if (!hasInitialized && isLoaded && isSignedIn && userContext && !isLoadingContext) {
      setHasInitialized(true);
      
      // Send welcome message if no messages exist
      if (messages.length === 0) {
        setTimeout(() => {
          sendWelcomeMessage();
        }, 500); // Small delay for better UX
      }
    }
  }, [hasInitialized, isLoaded, isSignedIn, userContext, isLoadingContext, messages.length, sendWelcomeMessage]);

  // Load user context when user is ready
  useEffect(() => {
    if (isLoaded && isSignedIn) {
      loadUserContext();
    }
  }, [isLoaded, isSignedIn, loadUserContext]);

  // Listen for credit updates
  useEffect(() => {
    const handleCreditUpdate = () => {
      console.log('[AIChatSidebar] Credit update event received, refreshing context...');
      loadUserContext();
    };
    
    window.addEventListener('creditUpdated', handleCreditUpdate);
    
    return () => {
      window.removeEventListener('creditUpdated', handleCreditUpdate);
    };
  }, [loadUserContext]);

  // Handle file uploads
  const handleFileUpload = useCallback(async (files: File[]) => {
    try {
      // Add files to session
      addUploadedFiles(files);
      
      // Convert first file to URL for display
      if (files.length > 0) {
        const firstFile = files[0];
        const imageUrl = URL.createObjectURL(firstFile);
        setProductImage(imageUrl);
      }
    } catch (error) {
      console.error('[AIChatSidebar] Error handling file upload:', error);
    }
  }, [addUploadedFiles, setProductImage]);

  // Handle sending messages with streaming
  const handleSendMessage = useCallback(async (content: string | ContentItem[]) => {
    try {
      // Reset streaming state before new message
      resetStreamingState();
      
      // Add user message to conversation immediately
      addMessage({
        type: 'user',
        content: content
      });
      
      // Extract text and images for streaming (backward compatibility)
      let messageText = '';
      let images: string[] | undefined;
      
      if (typeof content === 'string') {
        messageText = content;
      } else {
        // Extract text and images from ContentItem array
        const textItems = content.filter(item => item.type === 'text');
        const imageItems = content.filter(item => item.type === 'image_url');
        
        messageText = textItems.map(item => item.text).join('\n');
        images = imageItems.map(item => item.image_url!.url);
        
        if (images.length === 0) images = undefined;
      }
      
      // Start streaming response
      await streamMessage(
        messageText, 
        images, 
        getConversationHistory(),
        {
          onComplete: (response) => {
            console.log('[AIChatSidebar] Streaming complete:', response);
            
            // Add delay to ensure StreamingMessage has time to process completion
            setTimeout(() => {
              // Only add message if we have a proper response with message content
              if (response && response.message) {
                // Aggregate multiple tool results for parallel tool calls
                const aggregatedResult = response.toolResults && response.toolResults.length > 0 
                  ? aggregateToolResults(response.toolResults)
                  : undefined;
                
                // Add completed message to conversation
                addMessage({
                  type: 'agent',
                  content: response.message,
                  functionResult: aggregatedResult
                });
                
                // Handle structured output for image generation (aggregate multiple images)
                if (response.structuredOutput && onStructuredOutput) {
                  console.log('[AIChatSidebar] Processing structured output:', response.structuredOutput);
                  onStructuredOutput(response.structuredOutput);
                }
                
                // Update user context if credits were used (sum total credits used)
                const totalCreditsUsed = response.toolResults?.reduce((sum: number, result: any) => 
                  sum + (result.data?.creditsUsed || 0), 0) || 0;
                const latestCreditsRemaining = response.toolResults?.find((result: any) => 
                  result.data?.creditsRemaining !== undefined)?.data?.creditsRemaining;
                  
                if (latestCreditsRemaining !== undefined) {
                  console.log('[AIChatSidebar] Updating user context with new credit balance:', latestCreditsRemaining, 'Total used:', totalCreditsUsed);
                  setUserContext(prev => prev ? { ...prev, credits: latestCreditsRemaining } : null);
                }
              }
            }, 100); // Small delay to let streaming complete
          },
          onError: (error) => {
            console.error('[AIChatSidebar] Streaming error:', error);
            
            // Add error message to conversation
            addMessage({
              type: 'agent',
              content: "I'm sorry, I encountered an error while processing your request. Please try again."
            });
            
            toast.error('Failed to process message');
          }
        }
      );
      
    } catch (error) {
      console.error('[AIChatSidebar] Error sending message:', error);
      toast.error('Failed to send message');
    }
  }, [streamMessage, resetStreamingState, addMessage, getConversationHistory, onStructuredOutput, setUserContext]);

  // Render loading state
  if (!isLoaded || isLoadingContext) {
    return (
      <div className="w-full h-full flex flex-col bg-white">
        {/* Loading Content */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-500 text-sm">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (isMobile) {
    // Mobile-specific layout with fixed chat input
    return (
      <div className="w-full h-full relative bg-white">
        {/* Main Content Area - Scrollable with bottom padding for fixed input */}
        <div className="h-full overflow-y-auto scroll-smooth pb-20">
          {/* Low Credits Warning - Fixed at top */}
          {userContext && userContext.credits < 5 && (
            <div className="mx-4 mt-4 mb-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-700">
                  {userContext.credits === 0
                    ? 'No credits remaining'
                    : `${userContext.credits} credits left`
                  }
                </p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.location.href = '/settings'}
                  className="bg-white text-gray-700 border-gray-300 hover:bg-gray-50 text-xs px-3 py-1"
                >
                  <CreditCard size={12} className="mr-1" />
                  Buy
                </Button>
              </div>
            </div>
          )}

          {/* Messages Area */}
          <div className="px-4 py-2 space-y-1">
            {messages.map((message, index) => (
              <MessageBubble
                key={message.id}
                message={message}
                isLatest={index === messages.length - 1}
              />
            ))}
            
            {/* Streaming Message */}
            <StreamingMessage
              streamingState={streamingState}
              onRetryToolCall={retryToolCall}
            />
            
            {/* Empty State */}
            {messages.length === 0 && !isTyping && hasInitialized && (
              <div className="flex flex-col items-center justify-center min-h-[60vh] text-center px-4">
                <div className="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center mb-6">
                  <Sparkles size={20} className="text-white" />
                </div>
                <h3 className="font-medium text-black mb-3 text-lg">
                  How can I help you today?
                </h3>
                <p className="text-gray-600 mb-6 max-w-sm leading-relaxed text-sm">
                  I&apos;m Yaya, your AI marketing assistant. I remember our conversations and help you create amazing content. Upload a product image and let&apos;s get started!
                </p>
              </div>
            )}
            
            {/* Scroll anchor */}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Chat Input - Fixed at bottom of viewport */}
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10">
          <ChatInput
            onSendMessage={handleSendMessage}
            onFileUpload={handleFileUpload}
            disabled={!userContext}
            isProcessing={isProcessing}
            isMobile={isMobile}
            placeholder={
              !userContext
                ? "Loading..."
                : userContext.credits === 0
                ? "Purchase credits to start creating..."
                : "Describe your product or idea..."
            }
          />
        </div>
      </div>
    );
  }

  // Desktop layout
  return (
    <div className="w-full h-full flex flex-col bg-white">
      {/* Low Credits Warning - Fixed at top */}
      {userContext && userContext.credits < 5 && (
        <div className="flex-shrink-0 mx-4 lg:mx-6 mt-2 lg:mt-4 mb-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              {userContext.credits === 0
                ? 'No credits remaining'
                : `${userContext.credits} credits left`
              }
            </p>
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.location.href = '/settings'}
              className="bg-white text-gray-700 border-gray-300 hover:bg-gray-50 text-xs px-3 py-1"
            >
              <CreditCard size={12} className="mr-1" />
              Buy
            </Button>
          </div>
        </div>
      )}

      {/* Messages Area - Properly constrained scrollable section */}
      <div className="flex-1 min-h-0 flex flex-col">
        <div className="flex-1 overflow-y-auto scroll-smooth px-4 lg:px-6 py-2 lg:py-4 space-y-1">
          {messages.map((message, index) => (
            <MessageBubble
              key={message.id}
              message={message}
              isLatest={index === messages.length - 1}
            />
          ))}
          
          {/* Streaming Message */}
          <StreamingMessage
            streamingState={streamingState}
            onRetryToolCall={retryToolCall}
          />
          
          {/* Empty State */}
          {messages.length === 0 && !isTyping && hasInitialized && (
            <div className="flex flex-col items-center justify-center h-full text-center px-4">
              <div className="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center mb-6">
                <Sparkles size={20} className="text-white" />
              </div>
              <h3 className="font-medium text-black mb-3 text-xl">
                How can I help you today?
              </h3>
              <p className="text-gray-600 mb-6 max-w-sm leading-relaxed text-[15px]">
                I&apos;m Yaya, your AI marketing assistant. I remember our conversations and help you create amazing content. Upload a product image and let&apos;s get started!
              </p>
            </div>
          )}
          
          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Chat Input - Fixed at bottom */}
      <div className="flex-shrink-0">
        <ChatInput
          onSendMessage={handleSendMessage}
          onFileUpload={handleFileUpload}
          disabled={!userContext}
          isProcessing={isProcessing}
          isMobile={isMobile}
          placeholder={
            !userContext
              ? "Loading..."
              : userContext.credits === 0
              ? "Purchase credits to start creating..."
              : "Tell me about your product or what you'd like to create..."
          }
        />
      </div>
    </div>
  );
}
